<template>
  <div
    v-if="top.length || bottom.length"
    ref="nav"
    class="lc-left-area relative flex shrink-0"
  >
    <!-- unocss扫描js，打包后没有对应的图标样式 -->
    <div class="hidden">
      <i class="i-custom-datasource-line" />
      <i class="i-custom-datasource-fill" />
      <i class="i-custom-outline-line" />
      <i class="i-custom-outline-fill" />
      <i class="i-custom-code-line" />
      <i class="i-custom-code-fill" />
      <i class="i-custom-state-line" />
      <i class="i-custom-state-fill" />
      <i class="i-custom-model-line" />
      <i class="i-custom-model-fill" />
      <i class="i-custom-material-line" />
      <i class="i-custom-material-fill" />
      <i class="i-custom-logic-line" />
      <i class="i-custom-logic-fill" />
      <i class="i-custom-schema" />
    </div>
    <div
      class="lc-left-area-box relative w-12 flex flex-col shadow-[1px_0px_3px_0px_rgba(0,0,0,0.09)]"
    >
      <div class="top flex flex-col items-center">
        <div
          v-for="(item, i) of top"
          :key="i"
          class="hover:text-primary h-18 w-full flex flex flex-col items-center justify-center b-b-1 b-[#F5F6F8] b-solid text-[#5C6778] icon-hover"
          :class="{ 'text-primary': activeIndex === i }"
          @click.stop="onClick(item, i)"
        >
          <div class="h-5 w-5" :class="[item.icon]"></div>
          <div
            class="mt-1 text-center leading-tight fs-12"
            :class="{
              'w-8 break-all': item.title.length === 4,
              'ws-nowrap': item.title.length <= 3,
            }"
          >
            {{ item.title }}
          </div>
        </div>
      </div>
      <div class="bottom mb-20 mt-a flex flex-col items-center">
        <div
          v-for="(item, i) of bottom"
          :key="i"
          class="hover:text-primary mt-2 h-18 w-full flex flex-col items-center justify-center icon-hover"
          :class="{ 'text-primary': activeIndex === top.length + i }"
          @click.stop="onClick(item, top.length + i)"
        >
          <div class="h-5 w-5" :class="[item.icon]"></div>
          <div
            class="mt-1 text-center leading-tight fs-12"
            :class="{
              'w-8 break-all': item.title.length === 4,
              'ws-nowrap': item.title.length <= 3,
            }"
          >
            {{ item.title }}
          </div>
        </div>
      </div>
    </div>
    <LCPanel
      :value="showPanel"
      :pin="panelConfig.pin"
      :title="panelConfig.title"
      :icon="panelConfig.iconFill"
      :hide-pin="panelConfig.hidePin"
      :offset="[48, 0]"
      :hide-header="panelConfig.hideHeader"
      class="b-x-1 border-solid"
      @input="(e) => (showPanel = e)"
      @update:pin="(v) => (panelConfig.pin = v)"
      @hide="hide"
    >
      <component :is="components[panelConfig.id]" ref="custom" @close="hide" />
    </LCPanel>
  </div>
</template>

<script>
// import Plugins from './index'
import LCPanel from '@common/panel'
import { clickOutside } from '../../utils'
import { setAPI } from 'sinitek-lowcode-shared'

export default {
  name: 'LCLeftArea',
  components: {
    LCPanel,
  },
  inject: ['$doc', 'message'],
  data() {
    return {
      top: [],
      bottom: [],
      activeIndex: null,
      showPanel: false,
      panelConfig: {},
      components: {},
    }
  },
  created() {
    this.$doc.getPlugins('leftArea', (plugins) => {
      this.top = []
      this.bottom = []
      plugins.forEach((e) => {
        this.components[e.id] = e.component
        if (e.align === 'top') {
          this.top.push(e)
        } else {
          this.bottom.push(e)
        }
      })
    })
  },
  mounted() {
    setAPI({
      hidePanel: this.hide,
      openPanel: this.openPanelById,
    })
  },
  methods: {
    openPanelById(id, args) {
      this.isOpening = true
      const items = [...this.top, ...this.bottom]
      const index = items.findIndex((e) => e.id === id)
      if (index === -1) {
        return
      }
      this.onClick(items[index], index, args)
      setTimeout(() => {
        this.isOpening = false
      })
    },
    checkChange() {
      if (this.$refs?.custom?.isChanged) {
        this?.message?.error({
          showClose: true,
          message: '有改动未保存，请先保存！',
        })
        return true
      }
      return false
    },
    onClick(item, i, args) {
      if (this.checkChange()) {
        return
      }
      this.activeIndex = i
      this.showPanel = true
      this.panelConfig = Object.assign({}, item, { pin: false })
      this.show(args)
    },
    show(args) {
      this.clean = clickOutside(this.$refs.nav, () => {
        this.hide()
      })
      setTimeout(() => {
        this.$refs.custom?.show?.(args)
      })
    },
    hide() {
      // 点击定位到代码时触发了outside事件, 使用isOpening来判断是否是点击打开的
      if (!this.showPanel || this.isOpening) return
      // 如果hide里面返回true，则视为hide里面有错误
      if (this.$refs.custom?.hide?.()) return
      if (this.checkChange()) {
        return
      }
      this.$doc.hotkey.setTarget(null)
      this.activeIndex = null
      this.showPanel = false
      this?.clean?.()
    },
    onClose() {},
  },
}
</script>
