// import '@unocss/reset/tailwind-compat.css'
import 'sinitek-css/dist/icons/iconfont.css'
import 'sinitek-css/dist/theme/default.css'
import 'sinitek-css/dist/theme/mars.css'
import 'sinitek-css/dist/theme/earth.css'

import Vue from 'vue'
import App from './App.vue'
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
import 'sinitek-css/dist/icons/iconfont.css'
import 'sinitek-css/dist/theme/default.css'
import 'sinitek-css/dist/theme/mars.css'
import 'sinitek-css/dist/theme/earth.css'
import 'sinitek-lowcode-simulator/dist/sinitek-lowcode-simulator.css'
import SinitekUI from 'sinitek-ui'
import { http } from 'sinitek-util'
import routes from '@/routes'
import { PopupManager } from 'element-ui/lib/utils/popup'
import VueRouter from 'vue-router'
import Form from '../index'

Vue.use(VueRouter)

if (process.env.NODE_ENV !== 'production') {
  // require('./mock/index.js')
}

const router = new VueRouter({
  routes,
})

// 添加低代码例子
// import './lowcode/lowcode.js'

Vue.use(ElementUI)

Vue.use(SinitekUI, { http, popupManager: PopupManager })
// Vue.config.productionTip = false
const customConfig = {
  httpRequest: {
    header: {
      accesstoken: '3975c34d-c3db-4817-805d-2bc2db85d921',
    },
  },
}

http.customizeHttpConfig(customConfig)
Vue.use(Form, {
  router,
  http,
})

new Vue({
  router,
  render: (h) => h(App),
}).$mount('#app')

export default http
